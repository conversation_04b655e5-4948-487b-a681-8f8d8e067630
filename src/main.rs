mod challenges;
mod utils;

fn main() {
    // let arg = std::env::args().nth(1).expect("No argument provided");

    // match arg.as_str() {
    //     "password_hashing" => challenges::password_hashing::run(),
    //     "help_me_unpack" => challenges::help_me_unpack::run(),
    //     "backup_restore" => challenges::backup_restore::run(),
    //     "brute_force_zip" => challenges::brute_force_zip::run(),
    //     "mini_miner" => challenges::mini_miner::run(),
    //     "tales_of_ssl" => challenges::tales_of_ssl::run(),
    //     "jotting_jwts" => challenges::jotting_jwts::run(),
    //     "basic_face_detection" => challenges::basic_face_detection::run(),
    //     _ => panic!("Unknown challenge"),
    // }

    use image::GenericImageView;
    use std::env;
    use tract_onnx::prelude::*;

    // Get image path from CLI args
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        eprintln!("Usage: {} <mtcnn.onnx> <image.png>", args[0]);
        std::process::exit(1);
    }
    let model_path = &args[1];
    let image_path = &args[2];

    // Load ONNX model without specifying input shape first
    println!("Loading model: {}", model_path);
    let mut model = tract_onnx::onnx().model_for_path(model_path).unwrap();

    // Inspect the model inputs
    println!("Model inputs:");
    for (i, input) in model.inputs.iter().enumerate() {
        println!("  Input {}: {:?}", i, input);
    }

    // Try to get the expected input shape from the model
    let input_fact = model.input_fact(0).unwrap();
    println!("Input fact: {:?}", input_fact);

    // Try different common MTCNN input shapes
    let possible_shapes = vec![
        tvec![1, 3, 160, 160], // Your current attempt
        tvec![1, 160, 160, 3], // NHWC format
        tvec![3, 160, 160],    // CHW without batch
        tvec![160, 160, 3],    // HWC without batch
        tvec![1, 3, 224, 224], // Common CNN input size
        tvec![1, 224, 224, 3], // NHWC 224x224
    ];

    let mut successful_model = None;

    for shape in possible_shapes {
        println!("Trying input shape: {:?}", shape);
        let test_model = tract_onnx::onnx()
            .model_for_path(model_path)
            .unwrap()
            .with_input_fact(0, InferenceFact::dt_shape(f32::datum_type(), shape.clone()));

        match test_model {
            Ok(m) => match m.into_optimized() {
                Ok(optimized) => match optimized.into_runnable() {
                    Ok(runnable) => {
                        println!("✓ Successfully loaded with shape: {:?}", shape);
                        successful_model = Some((runnable, shape));
                        break;
                    }
                    Err(e) => println!("✗ Failed to make runnable: {}", e),
                },
                Err(e) => println!("✗ Failed to optimize: {}", e),
            },
            Err(e) => println!("✗ Failed with input fact: {}", e),
        }
    }

    let (model, input_shape) =
        successful_model.expect("Could not find a working input shape for the model");

    // Load image and prepare input based on the successful shape
    let img = image::open(image_path).expect("Failed to load image");

    // Determine image size and format from the successful input shape
    let (height, width, channels, batch_size) = match input_shape.as_slice() {
        [b, c, h, w] => (*h as u32, *w as u32, *c as usize, *b as usize), // NCHW
        [b, h, w, c] => (*h as u32, *w as u32, *c as usize, *b as usize), // NHWC
        [c, h, w] => (*h as u32, *w as u32, *c as usize, 1),              // CHW
        [h, w, c] => (*h as u32, *w as u32, *c as usize, 1),              // HWC
        _ => panic!("Unsupported input shape: {:?}", input_shape),
    };

    println!(
        "Using image size: {}x{}, channels: {}, batch: {}",
        width, height, channels, batch_size
    );
    let resized = img.resize_exact(width, height, image::imageops::FilterType::Nearest);
    let rgb = resized.to_rgb8();

    // Create input tensor based on the shape format
    let input = match input_shape.as_slice() {
        [_, 3, h, w] => {
            // NCHW format [batch, channels, height, width]
            let mut input_data: Vec<f32> = Vec::new();
            for c in 0..3 {
                for y in 0..*h {
                    for x in 0..*w {
                        input_data.push(rgb.get_pixel(x as u32, y as u32)[c] as f32 / 255.0);
                    }
                }
            }
            tract_ndarray::Array4::from_shape_vec((1, 3, *h, *w), input_data)
                .unwrap()
                .into_tensor()
        }
        [_, h, w, 3] => {
            // NHWC format [batch, height, width, channels]
            let mut input_data: Vec<f32> = Vec::new();
            for y in 0..*h {
                for x in 0..*w {
                    for c in 0..3 {
                        input_data.push(rgb.get_pixel(x as u32, y as u32)[c] as f32 / 255.0);
                    }
                }
            }
            tract_ndarray::Array4::from_shape_vec((1, *h, *w, 3), input_data)
                .unwrap()
                .into_tensor()
        }
        [3, h, w] => {
            // CHW format [channels, height, width]
            let mut input_data: Vec<f32> = Vec::new();
            for c in 0..3 {
                for y in 0..*h {
                    for x in 0..*w {
                        input_data.push(rgb.get_pixel(x as u32, y as u32)[c] as f32 / 255.0);
                    }
                }
            }
            tract_ndarray::Array3::from_shape_vec((3, *h, *w), input_data)
                .unwrap()
                .into_tensor()
        }
        [h, w, 3] => {
            // HWC format [height, width, channels]
            let mut input_data: Vec<f32> = Vec::new();
            for y in 0..*h {
                for x in 0..*w {
                    for c in 0..3 {
                        input_data.push(rgb.get_pixel(x as u32, y as u32)[c] as f32 / 255.0);
                    }
                }
            }
            tract_ndarray::Array3::from_shape_vec((*h, *w, 3), input_data)
                .unwrap()
                .into_tensor()
        }
        _ => panic!("Unsupported input shape: {:?}", input_shape),
    };

    // Run inference
    println!("Running inference...");
    let result = model.run(tvec!(input.into())).unwrap();

    // Inspect model outputs
    println!("Model outputs: {} tensors", result.len());
    for (i, output) in result.iter().enumerate() {
        println!(
            "  Output {}: shape {:?}, type {:?}",
            i,
            output.shape(),
            output.datum_type()
        );
    }

    // Try to find face detection results
    let mut face_found = false;

    // MTCNN models can have different output formats
    // Try to find probabilities/scores in any of the outputs
    for (i, output) in result.iter().enumerate() {
        match output.to_array_view::<f32>() {
            Ok(array) => {
                println!(
                    "Output {} values (first 10): {:?}",
                    i,
                    array.iter().take(10).collect::<Vec<_>>()
                );

                // Check if any value looks like a probability > threshold
                for &value in array.iter() {
                    if value > 0.7 && value <= 1.0 {
                        println!("Found high confidence value: {} in output {}", value, i);
                        face_found = true;
                        break;
                    }
                }

                if face_found {
                    break;
                }
            }
            Err(e) => {
                println!("Could not convert output {} to f32 array: {}", i, e);
                // Try other data types
                if let Ok(array) = output.to_array_view::<i64>() {
                    println!(
                        "Output {} as i64 (first 10): {:?}",
                        i,
                        array.iter().take(10).collect::<Vec<_>>()
                    );
                }
            }
        }
    }

    println!("Face detected: {}", face_found);
}
