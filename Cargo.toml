[package]
name = "hackattic"
version = "0.1.0"
edition = "2024"

[dependencies]
base64 = "0.22.1"
sha2 = "0.10.9"
pbkdf2 = "0.12.2"
hex = "0.4.3"
scrypt = "0.11"
hmac = "0.12.1"
flate2 = "1.1.2"
regex = "1.10.3"
reqwest = { version = "0.12.23", features = ["blocking", "json"] }
serde = { version = "1.0.226", features = ["derive"] }
serde_json = { version = "1.0.145" }
crossbeam-channel = "0.5.15"
num_cpus = "1.13"
ctrlc = "3.4"
dotenv = "0.15.0"
indexmap = { version = "2.0", features = ["serde"] }
openssl = "0.10.73"
nationify = { version = "0.2.1", features = ["iso_code"] }
warp = { version = "0.4.2", features = ["server"] }
jsonwebtoken = "9.3.1"
tokio = { version = "1.0", features = ["full"] }
image = "0.25"
tract-onnx = "0.21"
tract-core = "0.21"
