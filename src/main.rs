mod challenges;
mod mtcnn;
mod utils;

fn main() {
    // let arg = std::env::args().nth(1).expect("No argument provided");

    // match arg.as_str() {
    //     "password_hashing" => challenges::password_hashing::run(),
    //     "help_me_unpack" => challenges::help_me_unpack::run(),
    //     "backup_restore" => challenges::backup_restore::run(),
    //     "brute_force_zip" => challenges::brute_force_zip::run(),
    //     "mini_miner" => challenges::mini_miner::run(),
    //     "tales_of_ssl" => challenges::tales_of_ssl::run(),
    //     "jotting_jwts" => challenges::jotting_jwts::run(),
    //     "basic_face_detection" => challenges::basic_face_detection::run(),
    //     _ => panic!("Unknown challenge"),
    // }

    use std::env;

    // Parse command line arguments
    let args: Vec<String> = env::args().collect();

    if args.len() < 3 {
        eprintln!("Usage:");
        eprintln!("  Single model: {} <model.onnx> <image.png>", args[0]);
        eprintln!(
            "  All models:   {} <pnet.onnx> <rnet.onnx> <onet.onnx> <image.png>",
            args[0]
        );
        std::process::exit(1);
    }

    let mut mtcnn = mtcnn::MTCNN::new();
    let image_path;

    if args.len() == 3 {
        // Single model mode
        let model_path = &args[1];
        image_path = &args[2];

        if model_path.contains("pnet") {
            mtcnn.load_pnet(model_path).expect("Failed to load P-Net");
        } else if model_path.contains("rnet") {
            mtcnn.load_rnet(model_path).expect("Failed to load R-Net");
        } else if model_path.contains("onet") {
            mtcnn.load_onet(model_path).expect("Failed to load O-Net");
        } else {
            // Try to load as O-Net by default
            mtcnn
                .load_onet(model_path)
                .expect("Failed to load model as O-Net");
        }
    } else if args.len() == 5 {
        // All three models mode
        let pnet_path = &args[1];
        let rnet_path = &args[2];
        let onet_path = &args[3];
        image_path = &args[4];

        mtcnn.load_pnet(pnet_path).expect("Failed to load P-Net");
        mtcnn.load_rnet(rnet_path).expect("Failed to load R-Net");
        mtcnn.load_onet(onet_path).expect("Failed to load O-Net");
    } else {
        eprintln!("Invalid number of arguments");
        std::process::exit(1);
    }

    // Load and process the image
    println!("Loading image: {}", image_path);
    let img = image::open(image_path).expect("Failed to load image");

    // Run face detection
    let threshold = 0.7;
    match mtcnn.detect_faces(&img, threshold) {
        Ok(face_detected) => {
            println!("Face detected: {}", face_detected);
        }
        Err(e) => {
            eprintln!("Error during face detection: {}", e);
            std::process::exit(1);
        }
    }
}
