use image::{DynamicImage, GenericImageView, ImageReader};

pub fn run() {
    // Open the PNG file
    let img: DynamicImage = ImageReader::open("data/image_1.jpeg")
        .unwrap()
        .decode()
        .unwrap(); // decode into an image buffer

    let image_width = img.width();
    let image_height = img.height();

    let sub_image_width = image_width / 8;
    let sub_image_height = image_height / 8;

    for y in 0..8 {
        for x in 0..8 {
            let sub_img = img.view(
                x * sub_image_width,
                y * sub_image_height,
                sub_image_width,
                sub_image_height,
            );

            let cropped_buffer = sub_img.to_image();
            cropped_buffer
                .save(format!("data/cropped/cropped_{}_{}.png", x, y))
                .unwrap();
        }
    }
}
