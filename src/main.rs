mod challenges;
mod utils;

fn main() {
    // let arg = std::env::args().nth(1).expect("No argument provided");

    // match arg.as_str() {
    //     "password_hashing" => challenges::password_hashing::run(),
    //     "help_me_unpack" => challenges::help_me_unpack::run(),
    //     "backup_restore" => challenges::backup_restore::run(),
    //     "brute_force_zip" => challenges::brute_force_zip::run(),
    //     "mini_miner" => challenges::mini_miner::run(),
    //     "tales_of_ssl" => challenges::tales_of_ssl::run(),
    //     "jotting_jwts" => challenges::jotting_jwts::run(),
    //     "basic_face_detection" => challenges::basic_face_detection::run(),
    //     _ => panic!("Unknown challenge"),
    // }

    use image::GenericImageView;
    use std::env;
    use tract_onnx::prelude::*;

    // Get image path from CLI args
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        eprintln!("Usage: {} <mtcnn.onnx> <image.png>", args[0]);
        std::process::exit(1);
    }
    let model_path = &args[1];
    let image_path = &args[2];

    // Load ONNX model
    let model = tract_onnx::onnx()
        .model_for_path(model_path).unwrap()
        .with_input_fact(
            0,
            InferenceFact::dt_shape(f32::datum_type(), tvec![1, 3, 160, 160]),
        )?
        .into_optimized()?
        .into_runnable()?;

    // Load image
    let img = image::open(image_path).expect("Failed to load image");
    let resized = img.resize_exact(160, 160, image::imageops::FilterType::Nearest);

    // Convert to tensor [1, 3, 160, 160]
    let rgb = resized.to_rgb8();
    let mut input_data: Vec<f32> = Vec::new();
    for c in 0..3 {
        for y in 0..160 {
            for x in 0..160 {
                input_data.push(rgb.get_pixel(x, y)[c] as f32 / 255.0);
            }
        }
    }
    let input = tract_ndarray::Array4::from_shape_vec((1, 3, 160, 160), input_data).unwrap().into_tensor();

    // Run inference
    let result = model.run(tvec!(input.into())).unwrap();

    // NOTE: MTCNN usually returns multiple outputs (boxes, probs, landmarks).
    // Here, we assume the second output is detection scores.
    // You may need to adapt this depending on the ONNX model.
    let probs = result[1].to_array_view::<f32>().unwrap();

    // Check if any face probability > threshold (0.7)
    let mut face_found = false;
    for p in probs.iter() {
        if *p > 0.7 {
            face_found = true;
            break;
        }
    }

    println!("{}", face_found);
}
